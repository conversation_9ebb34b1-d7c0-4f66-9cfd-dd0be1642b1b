/* General reset */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f5f5;
  padding: 1rem;

 justify-content: center;

  
 
}

/* Container */
.container {
width: 70vw;
  margin: auto;
  padding: 1rem;

}

/* Card Styles */
.form-card,
.modal-content {
  background-color: #ffffff;
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

/* Form title */
.form-title {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  text-align: center;
}

/* Form and Grouping */
.form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 0.3rem;
  font-weight: bold;
}

.form-group input,
.form-group select {
  padding: 0.6rem;
  font-size: 1rem;
  border: 1px solid #ccc;
  border-radius: 0.5rem;
}

/* Buttons */
button {
  padding: 0.6rem 1rem;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 1rem;
}

.submit-button {
  background-color: #007bff;
  color: white;
}

.submit-button:disabled {
  background-color: #aaa;
  cursor: not-allowed;
}

.download-button {
  background-color: #28a745;
  color: white;
}

.edit-button {
  background-color: #ffc107;
  color: rgb(0, 0, 0);
}

.tile-button {
  background-color: #6f42c1;
  color: white;
}

/* Image Grid */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

/* Image Card */
.image-card {
  background-color: #fff;
  padding: 1rem;
  border-radius: 0.8rem;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.generated-image {
  max-width: 100%;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

/* Image Actions */
.image-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
  width: 100%;
}


.image-actions input[type="number"] {
  width: 60px;
  padding: 0.4rem;
  font-size: 0.9rem;
}



/* Modal */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  z-index: 1000;
}

.modal-content {
  width: 100%;
  max-width: 500px;
}

/* Error Messages */
.error-message {
  color: red;
  margin-top: 1rem;
  text-align: center;
}

/* Responsive Typography */
@media (max-width: 768px) {
  .form-title {
    font-size: 1.2rem;
  }

  button {
    font-size: 0.9rem;
    padding: 0.5rem 0.8rem;
  }
  .container {
width: 90vw;
  margin: auto;
  padding: 1rem;

}
}

@media (max-width: 480px) {
  .form-card,
  .modal-content {
    padding: 1rem;
  }

  .image-actions {
    flex-direction: column;
    align-items: stretch;
  }
  .container {
width: 90vw;
  margin: auto;
  padding: 1rem;

}
}

.image-actions {
  display: flex;
   flex-direction: column;
 align-items: stretch;
  justify-content: center;
  gap: 1rem;
  width: 100%;
}

.image-actions button {
  flex: 0 0 auto;
}

.tile-inputs {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: nowrap;
}

.tile-inputs label {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.95rem;
  white-space: nowrap;
}

.image-actions input[type="number"] {
  width: 100%;
  padding: 0.4rem;
  font-size: 0.9rem;
  border: 1px solid #ccc;
  border-radius: 0.5rem;
}

/* Mobile-specific layout */
@media (max-width: 768px) {
  .image-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .tile-inputs {
    flex-direction: column;
    align-items: stretch;
  }

  .tile-inputs label {
    flex-direction: column;
    align-items: flex-start;
  }
}
